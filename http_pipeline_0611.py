import json
import time

import torch
import cv2
import pandas as pd
import numpy as np
import torch.nn as nn
from timm import create_model
from torchvision import transforms
from ultralytics import YOL<PERSON>
from flask import Flask, request, jsonify
import logging
from typing import Dict, Any
from scipy.optimize import linear_sum_assignment
from filterpy.kalman import KalmanFilter
from bird_taxonomy import BirdTaxonomy

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ObjectTracker:
    """Tracks objects across frames and maintains classification history."""
    def __init__(self, max_disappeared=30, max_distance=100.0):
        # Store the next unique object ID along with active and disappeared objects
        self.next_object_id = 0
        self.objects = {}  # {object_id: centroid}
        self.disappeared = {}  # {object_id: count}
        self.classification_history = {}  # {object_id: [(label, confidence, timestamp, hierarchy_info), ...]}
        
        # 存储每个对象的最高置信度分类信息，改为嵌套字典结构，独立存储每个分类层级
        self.best_classifications = {}  # {object_id: {'species': {'name': name, 'score': score}, 'genus': {...}, ...}}
        
        # Maximum number of frames an object can be marked as disappeared
        self.max_disappeared = max_disappeared
        
        # Maximum distance between centroids to consider as the same object
        self.max_distance = max_distance
        
        # 卡尔曼滤波器字典，为每个跟踪对象创建一个滤波器
        self.kalman_filters = {}
        
        # 移动平均滤波窗口大小
        self.ma_window_size = 2
        self.position_history = {}  # {object_id: [(cx, cy), ...]}
        
        # 置信度衰减系数 - 每帧衰减因子，24帧/秒的视频中约5秒内从1降低到0.95
        self.confidence_decay_factor = 0.999
        
        # 存储最近被注销的对象信息，用于ID恢复
        self.recently_deregistered = {}  # {object_id: {'centroid': (x, y), 'timestamp': time, 'classification': {...}}}
        # 最近注销的对象保留时间（秒）
        self.deregistered_retention_time = 5.0
        # 恢复ID时使用的距离阈值，调整为更大的值以提高ID恢复成功率
        self.recovery_distance_threshold = 100.0
        
        # Load bird taxonomy information
        self.taxonomy = BirdTaxonomy('鸟类目科属种.xlsx')
    
    def register(self, centroid):
        """Register a new object with a new ID."""
        object_id = self.next_object_id
        self.objects[object_id] = centroid
        self.disappeared[object_id] = 0
        self.classification_history[object_id] = []
        self.position_history[object_id] = [centroid]
        
        # 初始化卡尔曼滤波器
        kf = KalmanFilter(dim_x=4, dim_z=2)  # 状态: [x, y, vx, vy], 观测: [x, y]
        dt = 1.0  # 时间步长
        
        # 状态转移矩阵 (F)
        kf.F = np.array([
            [1, 0, dt, 0],
            [0, 1, 0, dt],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        # 测量函数 (H)
        kf.H = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ])
        
        # 测量噪声协方差 (R)
        kf.R = np.array([
            [5, 0],
            [0, 5]
        ])
        
        # 过程噪声协方差 (Q)
        q = 0.1  # 过程噪声参数
        kf.Q = np.array([
            [q, 0, 0, 0],
            [0, q, 0, 0],
            [0, 0, q, 0],
            [0, 0, 0, q]
        ])
        
        # 后验误差协方差 (P)
        kf.P = np.array([
            [10, 0, 0, 0],
            [0, 10, 0, 0],
            [0, 0, 10, 0],
            [0, 0, 0, 10]
        ])
        
        # 初始状态 (x)
        kf.x = np.array([centroid[0], centroid[1], 0, 0]).reshape(4, 1)
        
        self.kalman_filters[object_id] = kf
        self.next_object_id += 1
        return object_id
    
    def deregister(self, object_id):
        """Deregister an object that has disappeared for too long.
        
        保存被注销对象的信息到recently_deregistered字典中，以便后续可能的ID恢复
        """
        # 保存对象信息到recently_deregistered字典中
        current_time = time.time()
        self.recently_deregistered[object_id] = {
            'centroid': self.objects[object_id],
            'timestamp': current_time,
            'classification': self.best_classifications.get(object_id, {}),
            'kalman_filter': self.kalman_filters.get(object_id, None),
            'position_history': self.position_history.get(object_id, [])
        }
        
        # 清理过期的recently_deregistered条目
        expired_ids = []
        for old_id, info in self.recently_deregistered.items():
            if current_time - info['timestamp'] > self.deregistered_retention_time:
                expired_ids.append(old_id)
        
        for old_id in expired_ids:
            del self.recently_deregistered[old_id]
        
        # 正常的注销流程
        del self.objects[object_id]
        del self.disappeared[object_id]
        del self.classification_history[object_id]
        if object_id in self.best_classifications:
            del self.best_classifications[object_id]
        if object_id in self.kalman_filters:
            del self.kalman_filters[object_id]
        if object_id in self.position_history:
            del self.position_history[object_id]
    
    def update(self, boxes):
        """Update tracked objects with new detections using Hungarian algorithm.
        
        Args:
            boxes: List of bounding boxes in format [x1, y1, x2, y2]
            
        Returns:
            Dictionary mapping box indices to object IDs
        """
        # 对所有对象的最高置信度进行衰减
        for object_id in self.best_classifications:
            for level in ['species', 'genus', 'family', 'order']:
                if self.best_classifications[object_id][level]['score'] > 0:
                    self.best_classifications[object_id][level]['score'] *= self.confidence_decay_factor
        
        # If no boxes, mark all existing objects as disappeared
        if len(boxes) == 0:
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            return {}
        
        # Calculate centroids of current boxes
        centroids = []
        for box in boxes:
            x1, y1, x2, y2 = box
            cx = (x1 + x2) / 2.0
            cy = (y1 + y2) / 2.0
            centroids.append((cx, cy))
        
        # If we have no objects yet, register all
        if len(self.objects) == 0:
            box_to_id = {}
            for i, centroid in enumerate(centroids):
                object_id = self.register(centroid)
                box_to_id[i] = object_id
            return box_to_id
        
        # 预测所有现有对象的新位置（使用卡尔曼滤波器）
        object_ids = list(self.objects.keys())
        predicted_centroids = []
        
        for object_id in object_ids:
            if object_id in self.kalman_filters:
                kf = self.kalman_filters[object_id]
                # 预测步骤
                kf.predict()
                predicted_centroid = (kf.x[0, 0], kf.x[1, 0])  # 提取预测的位置
                predicted_centroids.append(predicted_centroid)
            else:
                # 如果没有卡尔曼滤波器，使用最后已知位置
                predicted_centroids.append(self.objects[object_id])
        
        # 计算预测位置与新检测到的中心点之间的距离矩阵
        distances = np.zeros((len(predicted_centroids), len(centroids)))
        for i, predicted_centroid in enumerate(predicted_centroids):
            for j, centroid in enumerate(centroids):
                distances[i, j] = np.sqrt(
                    (predicted_centroid[0] - centroid[0]) ** 2 + 
                    (predicted_centroid[1] - centroid[1]) ** 2
                )
        
        # 应用匈牙利算法找到全局最优匹配
        # 如果距离大于max_distance，则设置为一个很大的值，避免匹配
        cost_matrix = distances.copy()
        cost_matrix[cost_matrix > self.max_distance] = 1000000  # 设置一个很大的值
        
        # 使用匈牙利算法找到最优匹配
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        # 创建匹配结果字典
        box_to_id = {}
        
        # 处理匹配结果
        for row, col in zip(row_indices, col_indices):
            # 如果距离太大，跳过此匹配
            if distances[row, col] > self.max_distance:
                continue
                
            # 获取对象ID
            object_id = object_ids[row]
            
            # 更新对象位置
            self.objects[object_id] = centroids[col]
            
            # 更新位置历史记录（用于移动平均滤波）
            if object_id in self.position_history:
                self.position_history[object_id].append(centroids[col])
                # 保持历史记录在窗口大小范围内
                if len(self.position_history[object_id]) > self.ma_window_size:
                    self.position_history[object_id] = self.position_history[object_id][-self.ma_window_size:]
                
                # 应用移动平均滤波
                if len(self.position_history[object_id]) >= 2:
                    # 计算平均位置
                    avg_x = sum(p[0] for p in self.position_history[object_id]) / len(self.position_history[object_id])
                    avg_y = sum(p[1] for p in self.position_history[object_id]) / len(self.position_history[object_id])
                    
                    # 使用平滑后的位置
                    smoothed_centroid = (avg_x, avg_y)
                    self.objects[object_id] = smoothed_centroid
            
            # 更新卡尔曼滤波器
            if object_id in self.kalman_filters:
                kf = self.kalman_filters[object_id]
                # 更新步骤
                measurement = np.array([centroids[col][0], centroids[col][1]]).reshape(2, 1)
                kf.update(measurement)
            
            # 重置消失计数
            self.disappeared[object_id] = 0
            
            # 记录匹配结果
            box_to_id[col] = object_id
        
        # 处理未匹配的现有对象（可能消失的对象）
        matched_rows = set(row_indices)
        unmatched_rows = set(range(len(object_ids))) - matched_rows
        
        for row in unmatched_rows:
            object_id = object_ids[row]
            self.disappeared[object_id] += 1
            if self.disappeared[object_id] > self.max_disappeared:
                self.deregister(object_id)
        
        # 处理未匹配的新检测（新出现的对象）
        matched_cols = set(col_indices)
        unmatched_cols = set(range(len(centroids))) - matched_cols
        
        for col in unmatched_cols:
            # 尝试恢复最近消失的ID
            recovered_id = None
            current_centroid = centroids[col]
            
            # 检查是否有最近消失的对象在相似位置
            for old_id, info in self.recently_deregistered.items():
                old_centroid = info['centroid']
                distance = np.sqrt(
                    (old_centroid[0] - current_centroid[0]) ** 2 + 
                    (old_centroid[1] - current_centroid[1]) ** 2
                )
                
                # 如果距离小于恢复阈值，恢复此ID
                if distance < self.recovery_distance_threshold:
                    recovered_id = old_id
                    
                    # 恢复对象信息
                    self.objects[old_id] = current_centroid
                    self.disappeared[old_id] = 0
                    
                    # 恢复分类历史
                    if old_id not in self.classification_history:
                        self.classification_history[old_id] = []
                    
                    # 恢复最佳分类信息
                    if 'classification' in info and info['classification']:
                        self.best_classifications[old_id] = info['classification']
                    
                    # 恢复卡尔曼滤波器
                    if 'kalman_filter' in info and info['kalman_filter'] is not None:
                        self.kalman_filters[old_id] = info['kalman_filter']
                        # 更新卡尔曼滤波器的状态
                        kf = self.kalman_filters[old_id]
                        measurement = np.array([current_centroid[0], current_centroid[1]]).reshape(2, 1)
                        kf.update(measurement)
                    else:
                        # 如果没有保存卡尔曼滤波器，创建一个新的
                        kf = KalmanFilter(dim_x=4, dim_z=2)
                        dt = 1.0
                        kf.F = np.array([
                            [1, 0, dt, 0],
                            [0, 1, 0, dt],
                            [0, 0, 1, 0],
                            [0, 0, 0, 1]
                        ])
                        kf.H = np.array([
                            [1, 0, 0, 0],
                            [0, 1, 0, 0]
                        ])
                        kf.R = np.array([
                            [5, 0],
                            [0, 5]
                        ])
                        q = 0.1
                        kf.Q = np.array([
                            [q, 0, 0, 0],
                            [0, q, 0, 0],
                            [0, 0, q, 0],
                            [0, 0, 0, q]
                        ])
                        kf.P = np.array([
                            [10, 0, 0, 0],
                            [0, 10, 0, 0],
                            [0, 0, 10, 0],
                            [0, 0, 0, 10]
                        ])
                        kf.x = np.array([current_centroid[0], current_centroid[1], 0, 0]).reshape(4, 1)
                        self.kalman_filters[old_id] = kf
                    
                    # 恢复位置历史
                    if 'position_history' in info and info['position_history']:
                        self.position_history[old_id] = info['position_history']
                    else:
                        self.position_history[old_id] = [current_centroid]
                    
                    # 从recently_deregistered中移除此ID
                    del self.recently_deregistered[old_id]
                    break
            
            # 如果没有恢复ID，则注册新ID
            if recovered_id is None:
                object_id = self.register(current_centroid)
                box_to_id[col] = object_id
            else:
                box_to_id[col] = recovered_id
                
            # 确保所有未匹配的新检测都被添加到box_to_id中
            # 这是修复从边缘移入的新物体无法获得ID的关键
        
        return box_to_id
    
    def add_classification(self, object_id, label, confidence):
        """Add a classification result to an object's history.
        
        分别维护每个层级（种、属、科、目）的最高置信度，只在当前分类结果的某层级置信度超过历史最高分时更新该层级
        增加了置信度阈值，防止低置信度的分类替换高置信度的分类
        """
        if object_id in self.classification_history:
            # Get hierarchy information for the label
            hierarchy_info = self.taxonomy.get_taxonomy(label)
            if hierarchy_info:
                current_time = time.time()
                
                # 添加到历史记录
                self.classification_history[object_id].append({
                    'label': label,
                    'confidence': confidence,
                    'timestamp': current_time,
                    'order': hierarchy_info['order'],
                    'family': hierarchy_info['family'],
                    'genus': hierarchy_info['genus'],
                    'species': hierarchy_info['species']
                })
                
                # 初始化最高置信度分类信息（如果不存在）
                if object_id not in self.best_classifications:
                    self.best_classifications[object_id] = {
                        'species': {'name': hierarchy_info['species'], 'score': confidence},
                        'genus': {'name': hierarchy_info['genus'], 'score': confidence},
                        'family': {'name': hierarchy_info['family'], 'score': confidence},
                        'order': {'name': hierarchy_info['order'], 'score': confidence}
                    }
                    return
                
                # 分别检查并更新每个层级的最高置信度
                best_info = self.best_classifications[object_id]
                
                # 更新种级别
                species_name = hierarchy_info['species']
                if species_name:
                    current_species_score = best_info['species']['score']
                    if (confidence > current_species_score + 0.1 or 
                        (confidence > current_species_score and current_species_score < 0.6)):
                        best_info['species']['name'] = species_name
                        best_info['species']['score'] = confidence
                
                # 更新属级别
                genus_name = hierarchy_info['genus']
                if genus_name:
                    current_genus_score = best_info['genus']['score']
                    if (confidence > current_genus_score + 0.1 or 
                        (confidence > current_genus_score and current_genus_score < 0.6)):
                        best_info['genus']['name'] = genus_name
                        best_info['genus']['score'] = confidence
                
                # 更新科级别
                family_name = hierarchy_info['family']
                if family_name:
                    current_family_score = best_info['family']['score']
                    if (confidence > current_family_score + 0.1 or 
                        (confidence > current_family_score and current_family_score < 0.6)):
                        best_info['family']['name'] = family_name
                        best_info['family']['score'] = confidence
                
                # 更新目级别
                order_name = hierarchy_info['order']
                if order_name:
                    current_order_score = best_info['order']['score']
                    if (confidence > current_order_score + 0.1 or 
                        (confidence > current_order_score and current_order_score < 0.6)):
                        best_info['order']['name'] = order_name
                        best_info['order']['score'] = confidence

    def get_best_label(self, object_id, confidence_threshold=0.7):
        """Get the best label for an object based on classification history.
        
        根据客户端输入的置信度阈值，取出其对应符合要求的层级和置信度
        保持标签的稳定性，避免标签跳变
        
        Args:
            object_id: The ID of the object
            confidence_threshold: Threshold for high-confidence classifications
            
        Returns:
            The best label for the object, or None if all confidence scores are below threshold
        """
        if object_id not in self.best_classifications:
            return None
        
        best_info = self.best_classifications[object_id]
        
        # 按照种、属、科、目的顺序检查置信度是否超过阈值
        if best_info['species']['score'] >= confidence_threshold and best_info['species']['name']:
            return best_info['species']['name']
        
        if best_info['genus']['score'] >= confidence_threshold and best_info['genus']['name']:
            return best_info['genus']['name']
            
        if best_info['family']['score'] >= confidence_threshold and best_info['family']['name']:
            return best_info['family']['name']
            
        if best_info['order']['score'] >= confidence_threshold and best_info['order']['name']:
            return best_info['order']['name']
            
        # 如果所有层级都不满足条件，返回None表示没有高置信度的分类
        # 这样可以避免低置信度的种名被错误地与其他层级置信度绑定
        return None


class CameraMotionDetector:
    """摄像头运动检测和补偿类"""
    def __init__(self):
        self.prev_gray = None
        self.prev_features = None
        self.motion_history = []
        self.motion_buffer_size = 10

        # 特征检测器
        self.feature_detector = cv2.goodFeaturesToTrack
        self.feature_params = dict(
            maxCorners=100,
            qualityLevel=0.01,
            minDistance=10,
            blockSize=7
        )

        # 运动模式
        self.camera_motion_type = 'static'  # static, pan, zoom
        self.motion_confidence = 0.0

    def detect_camera_motion(self, current_frame):
        """检测摄像头运动模式"""
        gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        if self.prev_gray is None:
            self.prev_gray = gray
            self.prev_features = self.feature_detector(gray, **self.feature_params)
            return 'static', 0.0, np.eye(3)

        # 检测特征点
        current_features = self.feature_detector(gray, **self.feature_params)

        if self.prev_features is None or len(self.prev_features) < 10:
            self.prev_gray = gray
            self.prev_features = current_features
            return 'static', 0.0, np.eye(3)

        # 光流跟踪
        if len(self.prev_features) > 0:
            next_features, status, _ = cv2.calcOpticalFlowPyrLK(
                self.prev_gray, gray, self.prev_features, None,
                winSize=(15, 15), maxLevel=2,
                criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
            )

            # 筛选有效的特征点
            good_old = self.prev_features[status == 1]
            good_new = next_features[status == 1]

            if len(good_old) >= 8:
                # 计算单应性矩阵
                try:
                    homography, _ = cv2.findHomography(
                        good_old, good_new, cv2.RANSAC, 5.0
                    )

                    if homography is not None:
                        motion_type, confidence = self._analyze_homography(homography)

                        # 更新运动历史
                        self.motion_history.append((motion_type, confidence))
                        if len(self.motion_history) > self.motion_buffer_size:
                            self.motion_history.pop(0)

                        # 平滑运动类型
                        self.camera_motion_type, self.motion_confidence = self._smooth_motion_type()

                        self.prev_gray = gray
                        self.prev_features = current_features

                        return self.camera_motion_type, self.motion_confidence, homography

                except Exception as e:
                    logger.warning(f"Homography calculation failed: {e}")

        self.prev_gray = gray
        self.prev_features = current_features
        return 'static', 0.0, np.eye(3)

    def _analyze_homography(self, H):
        """分析单应性矩阵确定运动类型"""
        if H is None:
            return 'static', 0.0

        # 提取变换参数
        scale_x = np.sqrt(H[0,0]**2 + H[1,0]**2)
        scale_y = np.sqrt(H[0,1]**2 + H[1,1]**2)
        translation_x = H[0,2]
        translation_y = H[1,2]

        # 计算运动幅度
        scale_change = abs(scale_x - 1.0) + abs(scale_y - 1.0)
        translation_magnitude = np.sqrt(translation_x**2 + translation_y**2)

        # 判断运动类型
        if scale_change > 0.05:  # 缩放阈值
            return 'zoom', min(scale_change * 10, 1.0)
        elif translation_magnitude > 5:  # 平移阈值
            return 'pan', min(translation_magnitude / 50, 1.0)
        else:
            return 'static', max(0, 1.0 - translation_magnitude / 5)

    def _smooth_motion_type(self):
        """平滑运动类型判断"""
        if not self.motion_history:
            return 'static', 0.0

        # 统计最近的运动类型
        recent_motions = [motion[0] for motion in self.motion_history[-5:]]
        recent_confidences = [motion[1] for motion in self.motion_history[-5:]]

        # 找出最常见的运动类型
        from collections import Counter
        motion_counts = Counter(recent_motions)
        most_common_motion = motion_counts.most_common(1)[0][0]

        # 计算平均置信度
        avg_confidence = np.mean(recent_confidences)

        return most_common_motion, avg_confidence


class BirdDetector:
    def __init__(self, yolo_path, classifier_path, class_dict_path, device=None):
        if device is None:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device

        logger.info(f"Initializing BirdDetector on device: {self.device}")

        self.yolo_model = YOLO(yolo_path).to(self.device)
        self.class_dict = self._load_class_dict(class_dict_path)
        num_classes = len(self.class_dict)
        self.classifier = self._build_classifier(classifier_path, num_classes)

        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

        # 超分辨率模块 - 针对小目标优化
        self.sr = cv2.dnn_superres.DnnSuperResImpl_create()
        self.sr.readModel('FSRCNN_x4.pb')
        self.sr.setModel('fsrcnn', 4)

        # 小目标检测优化参数
        self.enable_super_resolution = True  # 启用超分辨率
        self.min_detection_size = 8  # 最小检测尺寸（像素）
        self.multi_scale_factors = [1.0, 1.5, 2.0]  # 多尺度检测
        self.use_temporal_consistency = True  # 时间一致性
        self.small_object_threshold = 20  # 小目标阈值（像素）

        # Initialize object tracker with optimized parameters for small objects
        self.tracker = ObjectTracker(max_disappeared=15, max_distance=50.0)  # 减小距离阈值

        # Store the last processed frame and results for temporal consistency
        self.last_frame = None
        self.last_results = None

        # 时间一致性缓存
        self.frame_buffer = []
        self.buffer_size = 5  # 增加缓存以支持运动分析

        # 摄像头运动检测和补偿
        self.camera_motion_detector = CameraMotionDetector()
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=500, varThreshold=50, detectShadows=True
        )
        self.bg_learning_rate = 0.01  # 默认背景学习率

        # 运动不一致性检测参数
        self.motion_inconsistency_threshold = 0.3  # 运动不一致性阈值
        self.enable_motion_analysis = True  # 启用运动分析

        # 光流检测器
        self.lk_params = dict(
            winSize=(15, 15),
            maxLevel=2,
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
        )

        logger.info("BirdDetector initialization completed")

    def _enhance_image_for_small_objects(self, image):
        """增强图像以提高小目标检测效果"""
        enhanced_images = []

        # 原始图像
        enhanced_images.append(('original', image))

        # 如果启用超分辨率
        if self.enable_super_resolution:
            try:
                # 超分辨率增强
                sr_image = self.sr.upsample(image)
                enhanced_images.append(('super_resolution', sr_image))
            except Exception as e:
                logger.warning(f"Super resolution failed: {e}")

        # 多尺度增强
        for scale in self.multi_scale_factors:
            if scale != 1.0:
                h, w = image.shape[:2]
                new_h, new_w = int(h * scale), int(w * scale)
                scaled_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
                enhanced_images.append((f'scale_{scale}', scaled_image))

        # 对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        lab[:,:,0] = clahe.apply(lab[:,:,0])
        contrast_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        enhanced_images.append(('contrast', contrast_enhanced))

        # 锐化滤波
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(image, -1, kernel)
        enhanced_images.append(('sharpened', sharpened))

        return enhanced_images

    def _merge_multi_scale_detections(self, all_detections, original_shape=None):
        """合并多尺度检测结果"""
        merged_boxes = []
        merged_confidences = []

        for scale_name, detections in all_detections:
            if len(detections) == 0:
                continue

            boxes = detections['boxes']
            confidences = detections['confidences']

            # 将坐标转换回原始尺度
            if scale_name.startswith('scale_'):
                scale_factor = float(scale_name.split('_')[1])
                boxes = [[x1/scale_factor, y1/scale_factor, x2/scale_factor, y2/scale_factor]
                        for x1, y1, x2, y2 in boxes]
            elif scale_name == 'super_resolution':
                # 超分辨率是4倍放大
                boxes = [[x1/4, y1/4, x2/4, y2/4] for x1, y1, x2, y2 in boxes]

            merged_boxes.extend(boxes)
            merged_confidences.extend(confidences)

        if not merged_boxes:
            return [], []

        # 非极大值抑制
        boxes_array = np.array(merged_boxes)
        confidences_array = np.array(merged_confidences)

        # 使用OpenCV的NMS
        indices = cv2.dnn.NMSBoxes(
            boxes_array.tolist(),
            confidences_array.tolist(),
            score_threshold=0.2,  # 降低阈值以保留更多小目标
            nms_threshold=0.3     # 降低NMS阈值以减少重复
        )

        if len(indices) > 0:
            indices = indices.flatten()
            final_boxes = boxes_array[indices].tolist()
            final_confidences = confidences_array[indices].tolist()
            return final_boxes, final_confidences

        return [], []

    def _build_classifier(self, classifier_path, num_classes):
        """Build and load the Swin Transformer classifier model."""
        try:
            model = create_model(
                'swin_small_patch4_window7_224',
                pretrained=False,
                num_classes=num_classes,
                drop_rate=0.1,
                drop_path_rate=0.1
            )
            model.to(self.device)
            ckpt = torch.load(classifier_path, map_location=self.device)
            model.load_state_dict(ckpt['model_state_dict'])
            model.eval()
            return model
        except Exception as e:
            logging.error(f"Error in _build_classifier: {str(e)}")
            raise

    def _load_class_dict(self, class_dict_path):
        """Load the class dictionary from JSON file."""
        try:
            with open(class_dict_path, 'r', encoding='utf-8') as file:
                class_dict = json.load(file)
                # Convert string keys to integers for consistent lookup
                return {int(k): v for k, v in class_dict.items()}
        except Exception as e:
            logger.error(f"Error in _load_class_dict: {str(e)}")
            raise

    def detect_and_classify(self, image_array, threshold=0.3, use_tracking=True, confidence_threshold=0.7):
        """优化的小目标检测和分类方法"""
        # 多尺度检测策略
        enhanced_images = self._enhance_image_for_small_objects(image_array)
        all_detections = []

        # 对每个增强图像进行检测
        for scale_name, enhanced_image in enhanced_images:
            try:
                # YOLO检测，使用更低的阈值
                yolo_threshold = 0.15 if scale_name in ['super_resolution', 'contrast', 'sharpened'] else 0.25

                results = self.yolo_model(enhanced_image, conf=yolo_threshold, iou=0.3, imgsz=640)
                result = results[0]

                if len(result) > 0 and hasattr(result.boxes, 'xyxy'):
                    mask = result.boxes.conf.cpu().numpy() >= yolo_threshold
                    if any(mask):
                        boxes = result.boxes.xyxy[mask].cpu().numpy()
                        confidences = result.boxes.conf[mask].cpu().numpy()

                        # 过滤掉过小的检测框
                        valid_boxes = []
                        valid_confidences = []
                        for box, conf in zip(boxes, confidences):
                            x1, y1, x2, y2 = box
                            width = x2 - x1
                            height = y2 - y1
                            if width >= self.min_detection_size and height >= self.min_detection_size:
                                valid_boxes.append(box.tolist())
                                valid_confidences.append(float(conf))

                        all_detections.append((scale_name, {
                            'boxes': valid_boxes,
                            'confidences': valid_confidences
                        }))

            except Exception as e:
                logger.warning(f"Detection failed for {scale_name}: {e}")
                continue

        # 合并多尺度检测结果
        merged_boxes, merged_confidences = self._merge_multi_scale_detections(
            all_detections, image_array.shape[:2]
        )

        if not merged_boxes:
            self.last_frame = image_array.copy()
            self.last_results = {'boxes': [], 'labels': [], 'confidences': [], 'object_ids': [], 'detections_list': []}
            return self.last_results

        boxes = np.array(merged_boxes)
        confidences = np.array(merged_confidences)
        labels = []
        object_ids = []
        detections_list = []

        # Update object tracking
        if use_tracking:
            box_to_id = self.tracker.update(boxes)
        
        # Process each detected box with enhanced preprocessing for small objects
        for i, box in enumerate(boxes):
            x1, y1, x2, y2 = map(int, box)

            # 确保坐标在图像范围内
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(image_array.shape[1], x2)
            y2 = min(image_array.shape[0], y2)

            crop = image_array[y1:y2, x1:x2]

            # 检查裁剪区域是否有效
            if crop.size == 0 or crop.shape[0] < 3 or crop.shape[1] < 3:
                # 如果裁剪区域太小，跳过此检测
                continue

            # 针对小目标的特殊处理
            crop_height, crop_width = crop.shape[:2]
            is_small_object = crop_height <= self.small_object_threshold or crop_width <= self.small_object_threshold

            if is_small_object and self.enable_super_resolution:
                try:
                    # 对小目标使用超分辨率
                    crop = self.sr.upsample(crop)
                except Exception as e:
                    logger.warning(f"Super resolution failed for small object: {e}")

            # 额外的小目标增强
            if is_small_object:
                # 锐化
                kernel = np.array([[0,-1,0], [-1,5,-1], [0,-1,0]])
                crop = cv2.filter2D(crop, -1, kernel)

                # 对比度增强
                crop = cv2.convertScaleAbs(crop, alpha=1.2, beta=10)

            crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
            crop = self.transform(crop)
            crop = torch.unsqueeze(crop, 0).to(self.device)

            with torch.no_grad():
                outputs = self.classifier(crop)
                prob = nn.LogSoftmax(dim=1)(outputs)
                # 获取前5个预测结果
                top5_probs, top5_indices = torch.topk(prob, 5, dim=1)
                top5_probs = torch.exp(top5_probs).cpu().numpy()[0]  # 转换回概率
                top5_indices = top5_indices.cpu().numpy()[0]

                # 获取前5个类别名称和概率
                top5_detections = []
                for idx, prob_val in zip(top5_indices, top5_probs):
                    top5_detections.append({
                        "name": self.class_dict[idx],
                        "score": str(round(float(prob_val), 6))
                    })

                # 保存前5个预测结果
                detections_list.append(top5_detections)

                # 仍然保留最高置信度的预测作为主标签
                pred = top5_indices[0]
                label = self.class_dict[pred]
            # 使用分类模型的置信度而不是检测置信度
            confidence = float(top5_probs[0])  # 分类模型的置信度
            
            # If tracking is enabled, use tracking history for more consistent labeling
            if use_tracking:
                # 确保所有检测到的物体都能获得跟踪ID
                if i in box_to_id:
                    object_id = box_to_id[i]
                    object_ids.append(object_id)
                    
                    # Add this classification to the object's history
                    self.tracker.add_classification(object_id, label, confidence)
                    
                    # Get the best label based on history
                    best_label = self.tracker.get_best_label(object_id, confidence_threshold)
                    if best_label:
                        label = best_label
                else:
                    # 如果box_to_id中没有此索引，说明跟踪器没有正确处理这个检测框
                    # 手动注册一个新ID
                    x1, y1, x2, y2 = map(int, box.cpu().numpy())
                    cx = (x1 + x2) / 2.0
                    cy = (y1 + y2) / 2.0
                    object_id = self.tracker.register((cx, cy))
                    box_to_id[i] = object_id
                    object_ids.append(object_id)
                    
                    # 添加分类信息
                    self.tracker.add_classification(object_id, label, confidence)
            else:
                object_ids.append(-1)  # No tracking ID
                
            labels.append(label)

        # 创建一个新的列表来存储分类器的置信度，而不是YOLO的置信度
        classifier_confidences = []
        for i, top5_detection in enumerate(detections_list):
            if top5_detection and len(top5_detection) > 0:
                # 使用分类器的置信度
                classifier_confidences.append(float(top5_detection[0]['score']))
            else:
                # 如果没有分类器置信度，则使用YOLO的置信度作为后备
                classifier_confidences.append(confidences[i])
                
        # 基础检测结果
        base_results = {
            'boxes': boxes.tolist(),
            'labels': labels,
            'confidences': classifier_confidences,
            'object_ids': object_ids,
            'detections_list': detections_list
        }

        # 运动不一致性增强
        if self.enable_motion_analysis:
            motion_enhanced_results = self._detect_motion_inconsistency(image_array, base_results)
        else:
            motion_enhanced_results = base_results

        # 时间一致性增强
        if self.use_temporal_consistency and self.last_results is not None:
            results = self._apply_temporal_consistency(motion_enhanced_results)
        else:
            results = motion_enhanced_results

        # 更新帧缓存
        self.frame_buffer.append(results)
        if len(self.frame_buffer) > self.buffer_size:
            self.frame_buffer.pop(0)

        self.last_frame = image_array.copy()
        self.last_results = results

        return results

    def _apply_temporal_consistency(self, current_results):
        """应用时间一致性来稳定小目标检测"""
        if not self.frame_buffer:
            return current_results

        # 对于小目标，如果在前几帧中检测到相似位置的目标，增强当前检测的置信度
        enhanced_results = current_results.copy()

        for i, (box, confidence) in enumerate(zip(current_results['boxes'], current_results['confidences'])):
            x1, y1, x2, y2 = box
            current_center = ((x1 + x2) / 2, (y1 + y2) / 2)
            current_size = max(x2 - x1, y2 - y1)

            # 如果是小目标
            if current_size <= self.small_object_threshold:
                temporal_boost = 0

                # 检查历史帧中是否有相似位置的检测
                for past_frame in self.frame_buffer[-2:]:  # 检查最近2帧
                    for past_box, past_conf in zip(past_frame['boxes'], past_frame['confidences']):
                        px1, py1, px2, py2 = past_box
                        past_center = ((px1 + px2) / 2, (py1 + py2) / 2)

                        # 计算中心点距离
                        distance = np.sqrt((current_center[0] - past_center[0])**2 +
                                         (current_center[1] - past_center[1])**2)

                        # 如果距离很近，增加置信度
                        if distance < 30:  # 30像素内认为是同一目标
                            temporal_boost += 0.1 * past_conf

                # 应用时间一致性增强
                enhanced_results['confidences'][i] = min(1.0, confidence + temporal_boost)

        return enhanced_results

    def _detect_motion_inconsistency(self, current_frame, detections):
        """检测运动不一致性来增强鸟类检测"""
        if not self.enable_motion_analysis or len(detections['boxes']) == 0:
            return detections

        # 检测摄像头运动
        camera_motion_type, motion_confidence, homography = self.camera_motion_detector.detect_camera_motion(current_frame)

        # 背景减除
        fg_mask = self.background_subtractor.apply(current_frame, learningRate=self.bg_learning_rate)

        # 对每个检测框分析运动不一致性
        enhanced_confidences = []
        motion_scores = []

        for i, box in enumerate(detections['boxes']):
            x1, y1, x2, y2 = map(int, box)

            # 确保坐标在图像范围内
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(current_frame.shape[1], x2)
            y2 = min(current_frame.shape[0], y2)

            # 提取检测区域的前景掩码
            roi_mask = fg_mask[y1:y2, x1:x2]

            if roi_mask.size > 0:
                # 计算前景像素比例
                fg_ratio = np.sum(roi_mask > 0) / roi_mask.size

                # 分析运动模式
                motion_score = self._analyze_object_motion(
                    current_frame, box, camera_motion_type, homography
                )

                # 运动不一致性增强
                inconsistency_boost = 0
                if camera_motion_type == 'static':
                    # 静态摄像头：运动物体更可能是鸟类
                    inconsistency_boost = fg_ratio * 0.3
                elif camera_motion_type == 'pan':
                    # 平移摄像头：与背景运动不一致的物体更可能是鸟类
                    inconsistency_boost = motion_score * 0.2
                elif camera_motion_type == 'zoom':
                    # 缩放摄像头：尺寸变化不一致的物体更可能是鸟类
                    inconsistency_boost = motion_score * 0.15

                # 应用增强
                original_conf = detections['confidences'][i]
                enhanced_conf = min(1.0, original_conf + inconsistency_boost)
                enhanced_confidences.append(enhanced_conf)
                motion_scores.append(motion_score)
            else:
                enhanced_confidences.append(detections['confidences'][i])
                motion_scores.append(0.0)

        # 更新检测结果
        enhanced_detections = detections.copy()
        enhanced_detections['confidences'] = enhanced_confidences
        enhanced_detections['motion_scores'] = motion_scores
        enhanced_detections['camera_motion'] = {
            'type': camera_motion_type,
            'confidence': motion_confidence
        }

        return enhanced_detections

    def _analyze_object_motion(self, frame, box, camera_motion_type, homography):
        """分析单个目标的运动模式"""
        x1, y1, x2, y2 = map(int, box)

        # 计算目标中心点
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        # 如果有历史帧，分析运动轨迹
        if len(self.frame_buffer) >= 2:
            # 查找历史帧中相似位置的检测
            for past_frame in self.frame_buffer[-2:]:
                for past_box in past_frame.get('boxes', []):
                    px1, py1, px2, py2 = past_box
                    past_center_x = (px1 + px2) / 2
                    past_center_y = (py1 + py2) / 2

                    # 计算距离
                    distance = np.sqrt((center_x - past_center_x)**2 + (center_y - past_center_y)**2)

                    if distance < 50:  # 认为是同一目标
                        # 计算实际运动向量
                        actual_motion = np.array([center_x - past_center_x, center_y - past_center_y])

                        # 计算预期的背景运动（基于摄像头运动）
                        expected_motion = self._calculate_expected_motion(
                            past_center_x, past_center_y, homography
                        )

                        # 计算运动不一致性
                        motion_diff = np.linalg.norm(actual_motion - expected_motion)
                        motion_score = min(1.0, motion_diff / 20)  # 归一化到0-1

                        return motion_score

        return 0.0

    def _calculate_expected_motion(self, x, y, homography):
        """根据摄像头运动计算预期的背景运动"""
        if homography is None:
            return np.array([0, 0])

        # 将点通过单应性矩阵变换
        point = np.array([x, y, 1]).reshape(3, 1)
        transformed_point = homography @ point

        if transformed_point[2] != 0:
            transformed_x = transformed_point[0] / transformed_point[2]
            transformed_y = transformed_point[1] / transformed_point[2]

            expected_motion = np.array([transformed_x - x, transformed_y - y])
            return expected_motion

        return np.array([0, 0])

    def configure_for_small_objects(self, min_size=8, enable_sr=True, temporal_consistency=True, motion_analysis=True):
        """配置检测器以优化小目标检测"""
        self.min_detection_size = min_size
        self.enable_super_resolution = enable_sr
        self.use_temporal_consistency = temporal_consistency
        self.enable_motion_analysis = motion_analysis

        # 调整跟踪器参数
        self.tracker.max_distance = min(50.0, self.tracker.max_distance)
        self.tracker.max_disappeared = max(10, self.tracker.max_disappeared // 2)

        logger.info(f"Configured for small objects: min_size={min_size}, sr={enable_sr}, temporal={temporal_consistency}, motion={motion_analysis}")

    def configure_for_ptz_camera(self, motion_threshold=0.3, bg_learning_rate=0.01):
        """专门针对PTZ球机摄像头的配置"""
        self.motion_inconsistency_threshold = motion_threshold
        self.enable_motion_analysis = True

        # 重新配置背景减除器以适应PTZ摄像头
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=300,      # 减少历史帧数以快速适应场景变化
            varThreshold=30,  # 降低阈值以检测更细微的运动
            detectShadows=True
        )
        # 设置学习率（需要在apply时设置）
        self.bg_learning_rate = bg_learning_rate

        # 调整光流参数
        self.lk_params = dict(
            winSize=(21, 21),  # 增大窗口以提高稳定性
            maxLevel=3,
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01)
        )

        logger.info(f"Configured for PTZ camera: motion_threshold={motion_threshold}, bg_learning_rate={bg_learning_rate}")

    def get_detection_statistics(self):
        """获取检测统计信息"""
        if not self.last_results:
            return {}

        boxes = self.last_results.get('boxes', [])
        if not boxes:
            return {'total_detections': 0}

        sizes = []
        for box in boxes:
            x1, y1, x2, y2 = box
            width = x2 - x1
            height = y2 - y1
            sizes.append(max(width, height))

        small_objects = sum(1 for size in sizes if size <= self.small_object_threshold)

        return {
            'total_detections': len(boxes),
            'small_objects': small_objects,
            'average_size': np.mean(sizes) if sizes else 0,
            'min_size': min(sizes) if sizes else 0,
            'max_size': max(sizes) if sizes else 0
        }

    def process_and_format_results(self, detections: Dict, confidence_threshold: float) -> Dict[str, Any]:
        """Convert detections to API response format with hierarchical classification"""
        api_boxes = []

        try:
            bird_data = pd.read_excel('鸟类目科属种.xlsx', sheet_name='Sheet1')
        except Exception as e:
            logger.error(f"Error loading bird taxonomy data: {str(e)}")
            bird_data = None

        for i, (box, label, conf) in enumerate(zip(detections['boxes'], detections['labels'], detections['confidences'])):
            box_coords = [int(coord) for coord in box]
            
            top5_detections = detections['detections_list'][i] if 'detections_list' in detections and i < len(detections['detections_list']) else [{'name': label, 'score': str(round(float(conf), 6))}]
            
            object_id = detections['object_ids'][i] if 'object_ids' in detections and i < len(detections['object_ids']) else -1
            
            classification_info = {}
            if object_id != -1 and object_id in self.tracker.best_classifications:
                best_info = self.tracker.best_classifications[object_id]
                #confidence_threshold = 0.7  # 可根据需要调整
                best_label = self.tracker.get_best_label(object_id, confidence_threshold)
                
                # 如果所有层级置信度都低于阈值，best_label为None
                if best_label is None:
                    # 使用默认分类类型，避免低置信度的种名被错误地与其他层级置信度绑定
                    classification_type = 'order'  # 使用已有的层级类型，而不是'bird'
                    # 使用通用标签'鸟'
                    best_label = '鸟'
                else:
                    hierarchy_info = self.tracker.taxonomy.get_taxonomy(best_label)
                    classification_type = 'bird'
                    if hierarchy_info:
                        if best_label == hierarchy_info['species']:
                            classification_type = 'species'
                        elif best_label == hierarchy_info['genus']:
                            classification_type = 'genus'
                        elif best_label == hierarchy_info['family']:
                            classification_type = 'family'
                        elif best_label == hierarchy_info['order']:
                            classification_type = 'order'
                
                classification_info = {
                    'classification_type': classification_type,
                    'classification': best_label,
                    'species_score': best_info['species']['score'],
                    'genus_score': best_info['genus']['score'],
                    'family_score': best_info['family']['score'],
                    'order_score': best_info['order']['score'],
                    'species_name': best_info['species']['name'],
                    'genus_name': best_info['genus']['name'],
                    'family_name': best_info['family']['name'],
                    'order_name': best_info['order']['name']
                }
                # 使用最佳层级对应的置信度作为score
                # 确保classification_type是有效的键
                if classification_type in ['species', 'genus', 'family', 'order']:
                    final_score = best_info[f'{classification_type}']['score']
                else:
                    # 如果是自定义类型（如'order'表示'鸟'），使用order的分数
                    final_score = best_info['order']['score']
            else:
                classification_info = self._apply_hierarchical_classification(top5_detections, bird_data,confidence_threshold)
                final_score = float(conf)
            
            # 创建符合模板的基本结构
            box_info = {
                'name': classification_info.get('classification', label),
                'score': final_score,
                'box': box_coords
            }
            
            # 添加额外信息作为补充键值对
            box_info['detections'] = top5_detections
            box_info['classification_type'] = classification_info.get('classification_type', 'name')
            box_info['classification'] = classification_info.get('classification', label)
            box_info['species_score'] = classification_info.get('species_score', final_score)
            box_info['genus_score'] = classification_info.get('genus_score', final_score)
            box_info['family_score'] = classification_info.get('family_score', final_score)
            box_info['order_score'] = classification_info.get('order_score', final_score)
            box_info['species_name'] = classification_info.get('species_name', label)
            box_info['genus_name'] = classification_info.get('genus_name', None)
            box_info['family_name'] = classification_info.get('family_name', None)
            box_info['order_name'] = classification_info.get('order_name', None)
            
            if object_id != -1:
                box_info['tracking_id'] = int(object_id)

            # 添加运动分析信息
            if 'motion_scores' in detections and i < len(detections['motion_scores']):
                box_info['motion_score'] = detections['motion_scores'][i]

            api_boxes.append(box_info)

        # 构建返回结果
        result = {
            'result': 1 if api_boxes else 0,
            'msg': 'found target' if api_boxes else 'no target found',
            'boxes': api_boxes
        }

        # 添加摄像头运动信息
        if 'camera_motion' in detections:
            result['camera_motion'] = detections['camera_motion']

        return result
        
    def _apply_hierarchical_classification(self, detections, bird_data, confidence_threshold=0.7):
        """应用层级分类逻辑，同时计算目、科、属、种四个层级的概率"""
        if bird_data is None or len(detections) == 0:
            # 如果detections为空，返回默认值
            if len(detections) == 0:
                return {
                    'classification_type': 'order', 
                    'classification': '鸟',
                    'species_score': 0.0,
                    'genus_score': 0.0,
                    'family_score': 0.0,
                    'order_score': 0.0
                }
            # 否则使用第一个检测结果
            return {
                'classification_type': 'name', 
                'classification': detections[0]['name'],
                'species_score': float(detections[0]['score']),
                'genus_score': float(detections[0]['score']),
                'family_score': float(detections[0]['score']),
                'order_score': float(detections[0]['score'])
            }
            
        # 找到score最高的detection
        highest_score_detection = detections[0]  # 已经按置信度排序
        highest_score_name = highest_score_detection['name']
        highest_score = float(highest_score_detection['score'])
        
        # 初始化各层级的分类和概率
        species_name = highest_score_name
        species_score = highest_score
        genus_name = None
        genus_score = 0.0
        family_name = None
        family_score = 0.0
        order_name = None
        order_score = 0.0
        
        # 获取当前鸟类的分类信息
        try:
            # 获取当前鸟类的属、科、目信息
            genus_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '属'].values
            family_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '科'].values
            order_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '目'].values
            
            # 如果找到了分类信息
            if genus_values.size > 0 and family_values.size > 0 and order_values.size > 0:
                genus_name = genus_values[0]
                family_name = family_values[0]
                order_name = order_values[0]
                
                # 查找同属、同科、同目的鸟类
                same_genus_birds = bird_data[bird_data['属'] == genus_name]['中文名'].tolist()
                same_family_birds = bird_data[bird_data['科'] == family_name]['中文名'].tolist()
                same_order_birds = bird_data[bird_data['目'] == order_name]['中文名'].tolist()
                
                # 计算各层级的概率总和
                genus_score = species_score  # 初始化为种的概率
                family_score = species_score
                order_score = species_score
                
                # 累加同属、同科、同目鸟类的概率
                for detection in detections[1:]:  # 跳过第一个（已经计算过）
                    bird_name = detection['name']
                    bird_score = float(detection['score'])
                    
                    # 累加同属鸟类的概率
                    if bird_name in same_genus_birds:
                        genus_score += bird_score
                        family_score += bird_score  # 同属的鸟类也是同科同目的
                        order_score += bird_score
                    # 累加同科但不同属鸟类的概率
                    elif bird_name in same_family_birds:
                        family_score += bird_score
                        order_score += bird_score  # 同科的鸟类也是同目的
                    # 累加同目但不同科鸟类的概率
                    elif bird_name in same_order_birds:
                        order_score += bird_score
        except Exception as e:
            logger.error(f"计算层级概率时出错: {str(e)}")
        
        # 确定最终分类类型和结果
        classification_type = 'name'
        classification = species_name
        
        # 如果种的概率大于0.7，直接返回种名
        if species_score > confidence_threshold:
            classification_type = 'species'
            classification = species_name
        # 否则，如果属的概率大于0.7，返回属名
        elif genus_score > confidence_threshold and genus_name:
            classification_type = 'genus'
            classification = genus_name
        # 否则，如果科的概率大于0.7，返回科名
        elif family_score > confidence_threshold and family_name:
            classification_type = 'family'
            classification = family_name
        # 否则，如果目的概率大于0.7，返回目名
        elif order_score > confidence_threshold and order_name:
            classification_type = 'order'
            classification = order_name
        
        # 返回包含所有层级概率的结果
        return {
            'classification_type': classification_type,
            'classification': classification,
            'species_score': round(species_score, 6),
            'genus_score': round(genus_score, 6),
            'family_score': round(family_score, 6),
            'order_score': round(order_score, 6),
            'species_name': species_name,
            'genus_name': genus_name,
            'family_name': family_name,
            'order_name': order_name
        }


# Initialize Flask app
app = Flask(__name__)

# Initialize BirdDetector globally
detector = BirdDetector(
    yolo_path='./best0313.pt',
    classifier_path='./bestswin0610.pth',
    class_dict_path='./swin_transformer.json'
)

# 配置小目标检测优化
detector.configure_for_small_objects(
    min_size=8,           # 最小检测尺寸8像素
    enable_sr=True,       # 启用超分辨率
    temporal_consistency=True,  # 启用时间一致性
    motion_analysis=True  # 启用运动分析
)

# 配置PTZ球机摄像头优化
detector.configure_for_ptz_camera(
    motion_threshold=0.3,    # 运动不一致性阈值
    bg_learning_rate=0.01    # 背景学习率
)


@app.route('/agent/aidetect', methods=['POST'])
def detect():
    try:
        # Validate request
        if 'f0' not in request.files:
            return jsonify({
                'result': 0,
                'msg': 'No image file provided',
                'boxes': []
            }), 400

        # Get parameters
        camid = request.form.get('camid')
        if not camid:
            return jsonify({
                'result': 0,
                'msg': 'camid is required',
                'boxes': []
            }), 400

        thresh = float(request.form.get('thresh', 0.5))
        user_thresh = float(request.form.get('confidence_thresh', 0.7))
        # Read and decode image
        image_file = request.files['f0']
        image_bytes = image_file.read()
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            return jsonify({
                'result': 0,
                'msg': 'Invalid image format',
                'boxes': []
            }), 400

        # Process image with threshold
        logger.info(f"Processing image for camid: {camid}")
        detections = detector.detect_and_classify(image, threshold=thresh, confidence_threshold=user_thresh)
        results = detector.process_and_format_results(detections, user_thresh)

        # 添加检测统计信息
        stats = detector.get_detection_statistics()
        results['detection_stats'] = stats

        logger.info(f"Detection completed for camid: {camid}, stats: {stats}")
        return jsonify(results)

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            'result': 0,
            'msg': f'Error: {str(e)}',
            'boxes': []
        }), 500


@app.route('/agent/configure', methods=['POST'])
def configure_detector():
    """配置检测器参数"""
    try:
        data = request.get_json()

        min_size = data.get('min_detection_size', 8)
        enable_sr = data.get('enable_super_resolution', True)
        temporal_consistency = data.get('use_temporal_consistency', True)
        motion_analysis = data.get('enable_motion_analysis', True)

        detector.configure_for_small_objects(
            min_size=min_size,
            enable_sr=enable_sr,
            temporal_consistency=temporal_consistency,
            motion_analysis=motion_analysis
        )

        # PTZ摄像头特殊配置
        if data.get('ptz_camera_mode', False):
            motion_threshold = data.get('motion_threshold', 0.3)
            bg_learning_rate = data.get('bg_learning_rate', 0.01)

            detector.configure_for_ptz_camera(
                motion_threshold=motion_threshold,
                bg_learning_rate=bg_learning_rate
            )

        return jsonify({
            'result': 1,
            'msg': 'Configuration updated successfully',
            'config': {
                'min_detection_size': min_size,
                'enable_super_resolution': enable_sr,
                'use_temporal_consistency': temporal_consistency,
                'enable_motion_analysis': motion_analysis,
                'ptz_camera_mode': data.get('ptz_camera_mode', False)
            }
        })

    except Exception as e:
        logger.error(f"Error updating configuration: {str(e)}")
        return jsonify({
            'result': 0,
            'msg': f'Configuration error: {str(e)}'
        }), 500


@app.route('/agent/stats', methods=['GET'])
def get_detection_stats():
    """获取检测统计信息"""
    try:
        stats = detector.get_detection_statistics()
        return jsonify({
            'result': 1,
            'msg': 'Statistics retrieved successfully',
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        return jsonify({
            'result': 0,
            'msg': f'Statistics error: {str(e)}'
        }), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)  # Adjust port as needed
