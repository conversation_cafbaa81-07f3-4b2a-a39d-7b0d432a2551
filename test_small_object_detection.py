#!/usr/bin/env python3
"""
测试小目标检测优化效果的脚本
"""

import cv2
import numpy as np
import requests
import json
import time
import argparse
from pathlib import Path


def create_test_image_with_small_objects(width=704, height=576, num_objects=5):
    """创建包含小目标的测试图像"""
    # 创建背景
    image = np.random.randint(50, 150, (height, width, 3), dtype=np.uint8)
    
    # 添加一些纹理
    noise = np.random.randint(-30, 30, (height, width, 3), dtype=np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # 添加小目标（模拟鸟类）
    objects_info = []
    for i in range(num_objects):
        # 随机位置
        x = np.random.randint(20, width - 30)
        y = np.random.randint(20, height - 30)
        
        # 小目标尺寸 (8-15像素)
        size = np.random.randint(8, 16)
        
        # 创建椭圆形目标（模拟鸟类）
        color = (np.random.randint(100, 255), np.random.randint(100, 255), np.random.randint(100, 255))
        cv2.ellipse(image, (x, y), (size//2, size//3), 0, 0, 360, color, -1)
        
        # 添加一些细节
        cv2.circle(image, (x-size//4, y-size//6), 1, (0, 0, 0), -1)  # 眼睛
        
        objects_info.append({
            'x': x, 'y': y, 'size': size, 'color': color
        })
    
    return image, objects_info


def test_detection_api(image, api_url='http://localhost:5000/agent/aidetect', 
                      confidence_thresh=0.3):
    """测试检测API"""
    # 编码图像
    _, img_encoded = cv2.imencode('.jpg', image)
    
    # 准备请求
    files = {'f0': ('test.jpg', img_encoded.tobytes(), 'image/jpeg')}
    data = {
        'camid': 'test_camera',
        'thresh': 0.3,
        'confidence_thresh': confidence_thresh
    }
    
    try:
        start_time = time.time()
        response = requests.post(api_url, files=files, data=data, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            processing_time = end_time - start_time
            return result, processing_time
        else:
            print(f"API错误: {response.status_code}, {response.text}")
            return None, None
            
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None


def configure_detector(api_url='http://localhost:5000/agent/configure',
                      min_size=8, enable_sr=True, temporal_consistency=True,
                      motion_analysis=True, ptz_mode=False):
    """配置检测器"""
    config_data = {
        'min_detection_size': min_size,
        'enable_super_resolution': enable_sr,
        'use_temporal_consistency': temporal_consistency,
        'enable_motion_analysis': motion_analysis,
        'ptz_camera_mode': ptz_mode
    }

    if ptz_mode:
        config_data.update({
            'motion_threshold': 0.3,
            'bg_learning_rate': 0.01
        })
    
    try:
        response = requests.post(api_url, json=config_data, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"配置失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"配置请求失败: {e}")
        return None


def get_detection_stats(api_url='http://localhost:5000/agent/stats'):
    """获取检测统计信息"""
    try:
        response = requests.get(api_url, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"统计信息获取失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"统计信息请求失败: {e}")
        return None


def analyze_results(result, ground_truth_objects, processing_time):
    """分析检测结果"""
    if not result or result.get('result') != 1:
        return {
            'detected_count': 0,
            'ground_truth_count': len(ground_truth_objects),
            'processing_time': processing_time,
            'detection_rate': 0.0
        }
    
    boxes = result.get('boxes', [])
    stats = result.get('detection_stats', {})
    
    detected_count = len(boxes)
    ground_truth_count = len(ground_truth_objects)
    detection_rate = detected_count / ground_truth_count if ground_truth_count > 0 else 0
    
    # 分析检测到的目标尺寸
    detected_sizes = []
    for box in boxes:
        x1, y1, x2, y2 = box['box']
        width = x2 - x1
        height = y2 - y1
        size = max(width, height)
        detected_sizes.append(size)
    
    analysis = {
        'detected_count': detected_count,
        'ground_truth_count': ground_truth_count,
        'detection_rate': detection_rate,
        'processing_time': processing_time,
        'detected_sizes': detected_sizes,
        'avg_detected_size': np.mean(detected_sizes) if detected_sizes else 0,
        'detection_stats': stats
    }
    
    return analysis


def run_comprehensive_test():
    """运行综合测试"""
    print("=== 小目标检测优化测试 ===\n")
    
    # 测试不同配置
    test_configs = [
        {'name': '基础配置', 'min_size': 8, 'enable_sr': False, 'temporal': False, 'motion': False, 'ptz': False},
        {'name': '启用超分辨率', 'min_size': 8, 'enable_sr': True, 'temporal': False, 'motion': False, 'ptz': False},
        {'name': '完整优化', 'min_size': 8, 'enable_sr': True, 'temporal': True, 'motion': True, 'ptz': False},
        {'name': 'PTZ摄像头优化', 'min_size': 8, 'enable_sr': True, 'temporal': True, 'motion': True, 'ptz': True},
    ]
    
    results_summary = []
    
    for config in test_configs:
        print(f"\n--- 测试配置: {config['name']} ---")
        
        # 配置检测器
        config_result = configure_detector(
            min_size=config['min_size'],
            enable_sr=config['enable_sr'],
            temporal_consistency=config['temporal'],
            motion_analysis=config['motion'],
            ptz_mode=config['ptz']
        )
        
        if not config_result:
            print("配置失败，跳过此测试")
            continue
        
        print(f"配置成功: {config_result.get('msg', '')}")
        
        # 运行多次测试
        test_results = []
        for i in range(5):
            # 创建测试图像
            test_image, objects = create_test_image_with_small_objects(
                width=704, height=576, num_objects=np.random.randint(3, 8)
            )
            
            # 保存测试图像（可选）
            cv2.imwrite(f'test_image_{config["name"].replace(" ", "_")}_{i}.jpg', test_image)
            
            # 检测
            result, proc_time = test_detection_api(test_image, confidence_thresh=0.3)
            
            # 分析结果
            analysis = analyze_results(result, objects, proc_time)
            test_results.append(analysis)
            
            print(f"  测试 {i+1}: 检测到 {analysis['detected_count']}/{analysis['ground_truth_count']} 个目标, "
                  f"检测率: {analysis['detection_rate']:.2%}, 处理时间: {analysis['processing_time']:.2f}s")
        
        # 计算平均结果
        avg_detection_rate = np.mean([r['detection_rate'] for r in test_results])
        avg_processing_time = np.mean([r['processing_time'] for r in test_results])
        avg_detected_count = np.mean([r['detected_count'] for r in test_results])
        
        summary = {
            'config_name': config['name'],
            'avg_detection_rate': avg_detection_rate,
            'avg_processing_time': avg_processing_time,
            'avg_detected_count': avg_detected_count,
            'test_count': len(test_results)
        }
        
        results_summary.append(summary)
        
        print(f"  平均检测率: {avg_detection_rate:.2%}")
        print(f"  平均处理时间: {avg_processing_time:.2f}s")
        print(f"  平均检测数量: {avg_detected_count:.1f}")
    
    # 输出总结
    print("\n=== 测试总结 ===")
    for summary in results_summary:
        print(f"{summary['config_name']}:")
        print(f"  检测率: {summary['avg_detection_rate']:.2%}")
        print(f"  处理时间: {summary['avg_processing_time']:.2f}s")
        print(f"  平均检测数量: {summary['avg_detected_count']:.1f}")
        print()


def main():
    parser = argparse.ArgumentParser(description='测试小目标检测优化')
    parser.add_argument('--api-url', default='http://localhost:5000', 
                       help='API服务器URL')
    parser.add_argument('--test-image', help='测试特定图像文件')
    parser.add_argument('--comprehensive', action='store_true', 
                       help='运行综合测试')
    
    args = parser.parse_args()
    
    if args.comprehensive:
        run_comprehensive_test()
    elif args.test_image:
        # 测试特定图像
        image = cv2.imread(args.test_image)
        if image is None:
            print(f"无法读取图像: {args.test_image}")
            return
        
        print(f"测试图像: {args.test_image}")
        result, proc_time = test_detection_api(image)
        
        if result:
            print(f"检测结果: {result}")
            print(f"处理时间: {proc_time:.2f}s")
        else:
            print("检测失败")
    else:
        # 创建并测试单个图像
        print("创建测试图像...")
        test_image, objects = create_test_image_with_small_objects()
        cv2.imwrite('test_small_objects.jpg', test_image)
        print(f"创建了包含 {len(objects)} 个小目标的测试图像")
        
        print("测试检测...")
        result, proc_time = test_detection_api(test_image)
        
        if result:
            analysis = analyze_results(result, objects, proc_time)
            print(f"检测结果分析:")
            print(f"  检测到: {analysis['detected_count']}/{analysis['ground_truth_count']} 个目标")
            print(f"  检测率: {analysis['detection_rate']:.2%}")
            print(f"  处理时间: {analysis['processing_time']:.2f}s")
            if analysis['detected_sizes']:
                print(f"  平均检测尺寸: {analysis['avg_detected_size']:.1f} 像素")
        else:
            print("检测失败")


if __name__ == '__main__':
    main()
